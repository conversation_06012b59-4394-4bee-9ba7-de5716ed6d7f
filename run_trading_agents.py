#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents 运行脚本
基于用户要求的代码结构，使用硬编码的 API 密钥
"""

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def main():
    """主函数 - 按照用户要求的代码结构"""
    
    print("🎯 TradingAgents 多智能体金融交易分析")
    print("🔧 使用硬编码 API 密钥和中文界面")
    print()
    
    try:
        # 清理 ChromaDB 以避免冲突
        try:
            import chromadb
            from chromadb.config import Settings
            client = chromadb.Client(Settings(allow_reset=True))
            client.reset()
            print("🧹 已清理 ChromaDB 集合")
        except Exception as cleanup_error:
            print(f"⚠️ ChromaDB 清理警告: {cleanup_error}")
        
        print("📋 当前配置:")
        print(f"  LLM 提供商: {DEFAULT_CONFIG['llm_provider']}")
        print(f"  深度思考模型: {DEFAULT_CONFIG['deep_think_llm']}")
        print(f"  快速思考模型: {DEFAULT_CONFIG['quick_think_llm']}")
        print(f"  API 端点: {DEFAULT_CONFIG['backend_url']}")
        print(f"  OpenAI API Key: {DEFAULT_CONFIG['openai_api_key'][:10]}...")
        print(f"  FinnHub API Key: {DEFAULT_CONFIG['finnhub_api_key'][:10]}...")
        print()
        
        # 按照用户要求的代码结构
        print("🔧 正在初始化 TradingAgentsGraph...")
        ta = TradingAgentsGraph(debug=True, config=DEFAULT_CONFIG.copy())
        print("✅ TradingAgentsGraph 初始化成功")
        print()
        
        # forward propagate
        print("📊 开始分析 NVDA 股票 (2024-05-10)")
        print("⏳ 正在运行多智能体分析流程...")
        print()
        
        _, decision = ta.propagate("NVDA", "2024-05-10")
        
        print("🎯 分析完成！")
        print("=" * 60)
        print("📈 投资决策结果:")
        print(decision)
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        
        # 提供详细的错误分析和解决方案
        error_str = str(e)
        
        if "model_dump" in error_str:
            print("\n🔍 错误分析: API 响应格式不兼容")
            print("💡 解决方案:")
            print("  1. 使用标准 OpenAI API 端点")
            print("  2. 或者使用离线模式")
            print("\n🔧 修改建议:")
            print("  # 方案 1: 使用标准 OpenAI API")
            print("  config = DEFAULT_CONFIG.copy()")
            print("  config['backend_url'] = 'https://api.openai.com/v1'")
            print()
            print("  # 方案 2: 使用离线模式")
            print("  config = DEFAULT_CONFIG.copy()")
            print("  config['online_tools'] = False")
            
        elif "Collection" in error_str and "already exists" in error_str:
            print("\n🔍 错误分析: ChromaDB 集合冲突")
            print("💡 解决方案: 重启 Python 进程或清理数据库")
            
        elif "credentials" in error_str.lower() or "401" in error_str:
            print("\n🔍 错误分析: API 认证失败")
            print("💡 解决方案: 检查 API 密钥是否有效")
            
        elif "connection" in error_str.lower() or "network" in error_str.lower():
            print("\n🔍 错误分析: 网络连接问题")
            print("💡 解决方案: 检查网络连接和 API 端点可访问性")
            
        else:
            print(f"\n🔍 未知错误: {error_str}")
            print("💡 建议: 检查所有依赖包是否正确安装")
        
        print("\n📝 完整错误信息:")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
