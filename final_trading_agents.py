#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本的 TradingAgents 运行脚本
包含用户要求的代码结构和解决方案

使用硬编码的 API 密钥：
- OpenAI API Key: sk-yOXwTRVHIub4m6WjEWin68sqvdYypExLyBbChOc38SX4PnpW
- FinnHub API Key: d1htbc1r01qhsrhc1180d1htbc1r01qhsrhc118g
- 中文界面本地化已完成
"""

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG

def cleanup_chromadb():
    """清理 ChromaDB 集合以避免冲突"""
    try:
        import chromadb
        from chromadb.config import Settings
        client = chromadb.Client(Settings(allow_reset=True))
        client.reset()
        print("🧹 已清理 ChromaDB 集合")
    except Exception as e:
        print(f"⚠️ ChromaDB 清理警告: {e}")

def main():
    """
    主函数 - 按照用户要求的代码结构
    
    原始要求的代码：
    from tradingagents.graph.trading_graph import TradingAgentsGraph
    from tradingagents.default_config import DEFAULT_CONFIG
    
    ta = TradingAgentsGraph(debug=True, config=DEFAULT_CONFIG.copy())
    
    # forward propagate
    _, decision = ta.propagate("NVDA", "2024-05-10")
    print(decision)
    """
    
    print("🎯 TradingAgents 多智能体金融交易分析系统")
    print("🔧 使用硬编码 API 密钥和中文界面")
    print("🌟 已完成中文本地化")
    print()
    
    # 清理数据库
    cleanup_chromadb()
    
    print("📋 硬编码配置信息:")
    print(f"  OpenAI API Key: {DEFAULT_CONFIG['openai_api_key'][:15]}...")
    print(f"  FinnHub API Key: {DEFAULT_CONFIG['finnhub_api_key'][:15]}...")
    print(f"  API 端点: {DEFAULT_CONFIG['backend_url']}")
    print(f"  LLM 提供商: {DEFAULT_CONFIG['llm_provider']}")
    print()
    
    # 由于 Nuwa API 兼容性问题，提供两种解决方案
    print("🔧 检测到 API 兼容性问题，提供解决方案:")
    print("  方案 1: 使用离线模式（推荐用于测试）")
    print("  方案 2: 使用标准 OpenAI API（需要有效的 API key）")
    print()
    
    # 方案 1: 离线模式（推荐）
    print("🚀 方案 1: 离线模式")
    print("=" * 50)
    
    try:
        # 按照用户要求的代码结构，但添加离线配置
        config = DEFAULT_CONFIG.copy()
        config['online_tools'] = False  # 关键修改：使用离线模式
        
        print("🔧 正在初始化 TradingAgentsGraph (离线模式)...")
        ta = TradingAgentsGraph(debug=True, config=config)
        print("✅ TradingAgentsGraph 初始化成功")
        print()
        
        # forward propagate
        print("📊 开始分析 NVDA 股票 (2024-05-10)")
        print("⏳ 正在运行多智能体分析流程...")
        
        try:
            _, decision = ta.propagate("NVDA", "2024-05-10")
            
            print("🎯 离线模式分析完成！")
            print("=" * 60)
            print("📈 投资决策结果:")
            print(decision)
            print("=" * 60)
            return True
            
        except FileNotFoundError as e:
            print(f"📁 离线模式需要本地数据文件: {e}")
            print("✅ 但 TradingAgentsGraph 初始化成功，基础功能正常")
            print("💡 提示: 离线模式需要预先下载的历史数据文件")
            
    except Exception as e:
        print(f"❌ 离线模式失败: {e}")
    
    print("\n" + "=" * 60)
    print("🚀 方案 2: 标准 OpenAI API 模式")
    print("=" * 50)
    
    try:
        # 按照用户要求的代码结构，但使用标准 OpenAI API
        config = DEFAULT_CONFIG.copy()
        config['backend_url'] = 'https://api.openai.com/v1'  # 使用标准 OpenAI API
        
        print("🔧 正在初始化 TradingAgentsGraph (标准 OpenAI API)...")
        ta = TradingAgentsGraph(debug=True, config=config)
        print("✅ TradingAgentsGraph 初始化成功")
        print()
        
        # forward propagate
        print("📊 开始分析 NVDA 股票 (2024-05-10)")
        print("⏳ 正在运行多智能体分析流程...")
        
        _, decision = ta.propagate("NVDA", "2024-05-10")
        
        print("🎯 标准 API 模式分析完成！")
        print("=" * 60)
        print("📈 投资决策结果:")
        print(decision)
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ 标准 OpenAI API 模式失败: {e}")
        if "401" in str(e) or "authentication" in str(e).lower():
            print("🔑 可能的原因: API 密钥无效或已过期")
        elif "quota" in str(e).lower():
            print("💳 可能的原因: API 配额已用完")
    
    print("\n" + "=" * 60)
    print("📝 总结")
    print("=" * 60)
    print("✅ 已完成的配置:")
    print("  - OpenAI API Key 已硬编码")
    print("  - FinnHub API Key 已硬编码") 
    print("  - 所有英文界面已翻译为中文")
    print("  - API 端点已设置为 https://api.nuwaapi.com")
    print()
    print("⚠️ 发现的问题:")
    print("  - Nuwa API 响应格式与标准 OpenAI API 不兼容")
    print("  - 返回 HTML 页面而不是 JSON 响应")
    print()
    print("💡 建议的解决方案:")
    print("  1. 使用离线模式进行开发和测试")
    print("  2. 为 Nuwa API 创建自定义响应适配器")
    print("  3. 或者使用标准 OpenAI API 端点")
    print()
    print("🔧 可用的代码模板:")
    print("  # 离线模式")
    print("  config = DEFAULT_CONFIG.copy()")
    print("  config['online_tools'] = False")
    print("  ta = TradingAgentsGraph(debug=True, config=config)")
    print()
    print("  # 标准 OpenAI API 模式")
    print("  config = DEFAULT_CONFIG.copy()")
    print("  config['backend_url'] = 'https://api.openai.com/v1'")
    print("  ta = TradingAgentsGraph(debug=True, config=config)")

if __name__ == "__main__":
    main()
