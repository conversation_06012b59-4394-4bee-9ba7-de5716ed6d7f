#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TradingAgents 测试脚本
使用硬编码的 API 密钥和中文界面进行股票分析
"""

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
import traceback
import sys

def main():
    """主函数：运行 TradingAgents 分析"""
    
    print("🚀 启动 TradingAgents 股票分析系统")
    print("=" * 50)
    
    try:
        # 创建配置副本
        config = DEFAULT_CONFIG.copy()
        
        # 显示当前配置
        print("📋 当前配置:")
        print(f"  LLM 提供商: {config['llm_provider']}")
        print(f"  深度思考模型: {config['deep_think_llm']}")
        print(f"  快速思考模型: {config['quick_think_llm']}")
        print(f"  API 端点: {config['backend_url']}")
        print(f"  OpenAI API Key: {config['openai_api_key'][:10]}...")
        print(f"  FinnHub API Key: {config['finnhub_api_key'][:10]}...")
        print(f"  在线工具: {config['online_tools']}")
        print(f"  辩论轮次: {config['max_debate_rounds']}")
        print()
        
        # 初始化 TradingAgents
        print("🔧 正在初始化 TradingAgents...")
        ta = TradingAgentsGraph(debug=True, config=config)
        print("✅ TradingAgents 初始化成功")
        print()
        
        # 设置分析参数
        ticker = "NVDA"
        analysis_date = "2024-05-10"
        
        print(f"📊 开始分析股票: {ticker}")
        print(f"📅 分析日期: {analysis_date}")
        print("⏳ 正在运行多智能体分析流程...")
        print()
        
        # 执行分析
        _, decision = ta.propagate(ticker, analysis_date)
        
        # 显示结果
        print("🎯 分析完成！")
        print("=" * 50)
        print("📈 投资决策结果:")
        print(decision)
        print("=" * 50)
        
    except Exception as e:
        print(f"❌ 分析过程中发生错误: {e}")
        print("\n🔍 详细错误信息:")
        traceback.print_exc()
        
        # 提供故障排除建议
        print("\n💡 故障排除建议:")
        if "model_dump" in str(e):
            print("  - API 响应格式不兼容，建议使用标准 OpenAI API 端点")
            print("  - 或者设置 config['online_tools'] = False 使用离线模式")
        elif "credentials" in str(e).lower():
            print("  - 检查 API 密钥是否正确设置")
        elif "connection" in str(e).lower():
            print("  - 检查网络连接和 API 端点是否可访问")
        else:
            print("  - 检查所有依赖包是否正确安装")
            print("  - 确认配置文件中的所有设置都正确")
        
        return 1
    
    return 0

def test_offline_mode():
    """测试离线模式（使用本地缓存数据）"""

    print("🔄 尝试离线模式测试...")
    print("=" * 50)

    try:
        # 清理可能存在的 ChromaDB 集合
        try:
            import chromadb
            from chromadb.config import Settings
            client = chromadb.Client(Settings(allow_reset=True))
            client.reset()  # 重置所有集合
            print("🧹 已清理 ChromaDB 集合")
        except Exception as cleanup_error:
            print(f"⚠️ ChromaDB 清理警告: {cleanup_error}")

        # 创建离线配置
        config = DEFAULT_CONFIG.copy()
        config['online_tools'] = False  # 使用离线工具
        config['max_debate_rounds'] = 1  # 减少轮次以加快测试

        print("📋 离线模式配置:")
        print(f"  在线工具: {config['online_tools']}")
        print(f"  辩论轮次: {config['max_debate_rounds']}")
        print()

        # 初始化 TradingAgents
        print("🔧 正在初始化 TradingAgents (离线模式)...")
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ 离线模式初始化成功")

        return True

    except Exception as e:
        print(f"❌ 离线模式测试失败: {e}")
        if "Collection" in str(e) and "already exists" in str(e):
            print("💡 提示: ChromaDB 集合冲突，请重启 Python 进程或清理数据库")
        return False

if __name__ == "__main__":
    print("🎯 TradingAgents 多智能体金融交易分析系统")
    print("🔧 使用硬编码 API 密钥和中文界面")
    print()
    
    # 首先尝试正常模式
    result = main()
    
    # 如果正常模式失败，尝试离线模式测试
    if result != 0:
        print("\n" + "=" * 60)
        print("🔄 正常模式失败，尝试离线模式测试...")
        if test_offline_mode():
            print("✅ 离线模式测试成功 - 基础功能正常")
            print("💡 建议：使用离线模式或修复 API 兼容性问题")
        else:
            print("❌ 离线模式也失败 - 可能存在基础配置问题")
    
    sys.exit(result)
