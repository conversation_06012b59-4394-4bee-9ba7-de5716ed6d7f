#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的 TradingAgents 测试脚本
用于测试基本功能和 API 兼容性
"""

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
import traceback

def test_with_standard_openai():
    """使用标准 OpenAI API 进行测试"""
    
    print("🧪 测试 1: 使用标准 OpenAI API")
    print("=" * 40)
    
    try:
        config = DEFAULT_CONFIG.copy()
        config['backend_url'] = 'https://api.openai.com/v1'  # 标准 OpenAI API
        config['online_tools'] = False  # 使用离线工具避免额外 API 调用
        config['max_debate_rounds'] = 1
        
        print(f"📋 配置: {config['backend_url']}")
        print(f"🔧 在线工具: {config['online_tools']}")
        
        # 清理 ChromaDB
        try:
            import chromadb
            from chromadb.config import Settings
            client = chromadb.Client(Settings(allow_reset=True))
            client.reset()
        except:
            pass
        
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ 标准 OpenAI API 初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 标准 OpenAI API 测试失败: {e}")
        return False

def test_with_nuwa_api():
    """使用 Nuwa API 进行测试"""
    
    print("\n🧪 测试 2: 使用 Nuwa API")
    print("=" * 40)
    
    try:
        config = DEFAULT_CONFIG.copy()
        config['backend_url'] = 'https://api.nuwaapi.com'  # Nuwa API
        config['online_tools'] = False  # 使用离线工具
        config['max_debate_rounds'] = 1
        
        print(f"📋 配置: {config['backend_url']}")
        print(f"🔧 在线工具: {config['online_tools']}")
        
        # 清理 ChromaDB
        try:
            import chromadb
            from chromadb.config import Settings
            client = chromadb.Client(Settings(allow_reset=True))
            client.reset()
        except:
            pass
        
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ Nuwa API 初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ Nuwa API 测试失败: {e}")
        return False

def test_offline_only():
    """纯离线模式测试"""
    
    print("\n🧪 测试 3: 纯离线模式")
    print("=" * 40)
    
    try:
        config = DEFAULT_CONFIG.copy()
        config['online_tools'] = False
        config['max_debate_rounds'] = 1
        
        print(f"🔧 在线工具: {config['online_tools']}")
        
        # 清理 ChromaDB
        try:
            import chromadb
            from chromadb.config import Settings
            client = chromadb.Client(Settings(allow_reset=True))
            client.reset()
        except:
            pass
        
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ 纯离线模式初始化成功")
        
        # 尝试运行一个简单的分析（如果有本地数据）
        try:
            print("🔄 尝试运行简单分析...")
            # 注意：这可能会失败，因为需要本地数据文件
            # _, decision = ta.propagate("NVDA", "2024-05-10")
            # print("✅ 离线分析成功")
            print("⚠️ 跳过实际分析（需要本地数据文件）")
        except Exception as analysis_error:
            print(f"⚠️ 分析失败（预期，因为缺少本地数据）: {analysis_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 纯离线模式测试失败: {e}")
        return False

def test_api_response():
    """测试 API 响应格式"""
    
    print("\n🧪 测试 4: API 响应格式")
    print("=" * 40)
    
    try:
        from openai import OpenAI
        
        # 测试 Nuwa API 响应
        print("🔍 测试 Nuwa API 响应格式...")
        client = OpenAI(
            base_url=DEFAULT_CONFIG['backend_url'],
            api_key=DEFAULT_CONFIG['openai_api_key']
        )
        
        response = client.chat.completions.create(
            model='gpt-4o-mini',
            messages=[{'role': 'user', 'content': 'Hello'}],
            max_tokens=5
        )
        
        print(f"📊 响应类型: {type(response)}")
        print(f"📝 响应内容: {response}")
        
        if isinstance(response, str):
            print("⚠️ Nuwa API 返回字符串格式，与标准 OpenAI API 不兼容")
            return False
        else:
            print("✅ 响应格式兼容")
            return True
            
    except Exception as e:
        print(f"❌ API 响应测试失败: {e}")
        return False

def main():
    """主测试函数"""
    
    print("🎯 TradingAgents 兼容性测试")
    print("🔧 测试硬编码 API 密钥和基本功能")
    print()
    
    results = []
    
    # 运行所有测试
    results.append(("标准 OpenAI API", test_with_standard_openai()))
    results.append(("Nuwa API", test_with_nuwa_api()))
    results.append(("纯离线模式", test_offline_only()))
    results.append(("API 响应格式", test_api_response()))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 提供建议
    print("\n💡 建议:")
    if results[0][1]:  # 标准 OpenAI API 成功
        print("  - 基础功能正常，建议使用标准 OpenAI API")
    if not results[1][1]:  # Nuwa API 失败
        print("  - Nuwa API 存在兼容性问题，需要自定义适配器")
    if results[2][1]:  # 离线模式成功
        print("  - 离线模式可用，适合开发和测试")
    if not results[3][1]:  # API 响应格式问题
        print("  - 需要为 Nuwa API 创建响应格式转换器")

if __name__ == "__main__":
    main()
