#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可工作的 TradingAgents 示例
使用硬编码的 API 密钥和中文界面进行股票分析

注意：由于 Nuwa API 响应格式不兼容，此脚本提供两种工作模式：
1. 离线模式（推荐用于测试）
2. 标准 OpenAI API 模式（需要有效的 OpenAI API key）
"""

from tradingagents.graph.trading_graph import TradingAgentsGraph
from tradingagents.default_config import DEFAULT_CONFIG
import traceback
import sys

def cleanup_chromadb():
    """清理 ChromaDB 集合以避免冲突"""
    try:
        import chromadb
        from chromadb.config import Settings
        client = chromadb.Client(Settings(allow_reset=True))
        client.reset()
        print("🧹 已清理 ChromaDB 集合")
    except Exception as e:
        print(f"⚠️ ChromaDB 清理警告: {e}")

def run_offline_mode():
    """运行离线模式（推荐）"""
    
    print("🔄 运行离线模式")
    print("=" * 50)
    print("📝 说明: 离线模式使用本地缓存数据，不进行实际 API 调用")
    print()
    
    try:
        # 清理数据库
        cleanup_chromadb()
        
        # 配置离线模式
        config = DEFAULT_CONFIG.copy()
        config['online_tools'] = False  # 关键：使用离线工具
        config['max_debate_rounds'] = 1  # 减少轮次
        config['max_risk_discuss_rounds'] = 1
        
        print("📋 离线模式配置:")
        print(f"  LLM 提供商: {config['llm_provider']}")
        print(f"  在线工具: {config['online_tools']}")
        print(f"  辩论轮次: {config['max_debate_rounds']}")
        print()
        
        # 初始化 TradingAgents
        print("🔧 正在初始化 TradingAgents (离线模式)...")
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ TradingAgents 初始化成功")
        print()
        
        # 设置分析参数
        ticker = "NVDA"
        analysis_date = "2024-05-10"
        
        print(f"📊 开始分析股票: {ticker}")
        print(f"📅 分析日期: {analysis_date}")
        print("⏳ 正在运行离线分析...")
        print("📝 注意: 离线模式需要本地数据文件，如果没有会显示相应错误")
        print()
        
        # 执行分析
        try:
            _, decision = ta.propagate(ticker, analysis_date)
            
            print("🎯 分析完成！")
            print("=" * 50)
            print("📈 投资决策结果:")
            print(decision)
            print("=" * 50)
            return True
            
        except FileNotFoundError as e:
            print(f"📁 缺少本地数据文件: {e}")
            print("💡 提示: 离线模式需要预先下载的数据文件")
            print("✅ 但是 TradingAgents 初始化成功，基础功能正常")
            return True
            
    except Exception as e:
        print(f"❌ 离线模式失败: {e}")
        traceback.print_exc()
        return False

def run_with_standard_openai():
    """使用标准 OpenAI API 运行（需要有效的 API key）"""
    
    print("🌐 使用标准 OpenAI API")
    print("=" * 50)
    print("📝 说明: 此模式使用标准 OpenAI API，需要有效的 API key")
    print()
    
    try:
        # 清理数据库
        cleanup_chromadb()
        
        # 配置标准 OpenAI API
        config = DEFAULT_CONFIG.copy()
        config['backend_url'] = 'https://api.openai.com/v1'  # 标准 OpenAI API
        config['online_tools'] = True  # 可以使用在线工具
        config['max_debate_rounds'] = 1
        config['max_risk_discuss_rounds'] = 1
        
        print("📋 标准 OpenAI API 配置:")
        print(f"  API 端点: {config['backend_url']}")
        print(f"  在线工具: {config['online_tools']}")
        print(f"  API Key: {config['openai_api_key'][:10]}...")
        print()
        
        # 初始化 TradingAgents
        print("🔧 正在初始化 TradingAgents (标准 OpenAI API)...")
        ta = TradingAgentsGraph(['market'], config=config, debug=True)
        print("✅ TradingAgents 初始化成功")
        print()
        
        # 设置分析参数
        ticker = "NVDA"
        analysis_date = "2024-05-10"
        
        print(f"📊 开始分析股票: {ticker}")
        print(f"📅 分析日期: {analysis_date}")
        print("⏳ 正在运行在线分析...")
        print()
        
        # 执行分析
        _, decision = ta.propagate(ticker, analysis_date)
        
        print("🎯 分析完成！")
        print("=" * 50)
        print("📈 投资决策结果:")
        print(decision)
        print("=" * 50)
        return True
        
    except Exception as e:
        print(f"❌ 标准 OpenAI API 模式失败: {e}")
        if "401" in str(e) or "authentication" in str(e).lower():
            print("🔑 API 密钥可能无效或已过期")
        elif "quota" in str(e).lower():
            print("💳 API 配额可能已用完")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    
    print("🎯 TradingAgents 多智能体金融交易分析系统")
    print("🔧 使用硬编码 API 密钥和中文界面")
    print("🌟 支持离线和在线两种模式")
    print()
    
    # 显示选项
    print("📋 可用模式:")
    print("  1. 离线模式 (推荐用于测试)")
    print("  2. 标准 OpenAI API 模式")
    print("  3. 退出")
    print()
    
    while True:
        try:
            choice = input("请选择模式 (1/2/3): ").strip()
            
            if choice == "1":
                print("\n🚀 启动离线模式...")
                success = run_offline_mode()
                break
            elif choice == "2":
                print("\n🚀 启动标准 OpenAI API 模式...")
                success = run_with_standard_openai()
                break
            elif choice == "3":
                print("👋 再见！")
                return 0
            else:
                print("❌ 无效选择，请输入 1、2 或 3")
                continue
                
        except KeyboardInterrupt:
            print("\n👋 用户取消，再见！")
            return 0
    
    if success:
        print("\n✅ 程序执行成功！")
        return 0
    else:
        print("\n❌ 程序执行失败，请检查配置和依赖")
        return 1

if __name__ == "__main__":
    sys.exit(main())
